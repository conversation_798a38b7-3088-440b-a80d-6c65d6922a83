class HeaderAndFooterManager {
    constructor() {
        this.header = null;
        this.navMenu = null;
        this.hamburgerMenu = null;
        this.overlay = null;

        this.init();
    }

    init() {
        this.injectCSS();
        this.injectHTML();
        this.injectFooter();
        this.setupEventListeners();
        this.setupHamburgerMenu();
        this.setActiveNavigation();
    }

    injectCSS() {
        const style = document.createElement('style');
        style.textContent = `
            /* Header Styles - Desktop: Transparent mit Hamburger rechts */
            .header {
                background-color: transparent;
                padding: 0;
                text-align: center;
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                z-index: 1002;
                display: flex;
                align-items: center;
                justify-content: center;
                height: auto;
            }

            .header h1 {
                font-family: 'Montserrat', sans-serif;
                font-weight: 300;
                color: #fbd19a;
                font-size: 22px;
                letter-spacing: 1.5px;
                cursor: pointer;
                transition: color 0.3s ease;
                margin: 0;
                display: none; /* Versteckt auf Desktop */
            }

            .header h1:hover {
                color: #e6a870;
            }

            /* Hamburger Menu Button - Immer rechts */
            .hamburger-menu {
                position: fixed;
                right: 20px;
                top: 20px;
                background: none;
                border: none;
                cursor: pointer;
                padding: 8px;
                z-index: 1003;
                display: flex;
                flex-direction: column;
                gap: 4px;
                opacity: 1;
                visibility: visible;
            }

            .hamburger-line {
                width: 25px;
                height: 3px;
                background-color: #fbd19a;
                border-radius: 2px;
                transition: all 0.3s ease;
            }

            .hamburger-menu:hover .hamburger-line {
                background-color: #fbd19a;
            }

            /* Navigation Menu - Neue Farbpalette */
            .nav-menu {
                position: fixed;
                top: 0;
                right: -250px;
                width: 250px;
                height: 100vh;
                background: linear-gradient(135deg, #04070f 0%, #29303b 100%);
                z-index: 1000;
                transition: right 0.3s ease;
                box-shadow: -2px 0 10px rgba(0, 0, 0, 0.3);
                overflow-y: auto;
            }

            .nav-menu.active {
                right: 0;
            }

            /* Responsive menu width */
            @media (max-width: 480px) {
                .nav-menu {
                    width: 100vw;
                    right: -100vw;
                }

                .nav-menu.active {
                    right: 0;
                }
            }

            @media (max-width: 768px) and (min-width: 481px) {
                .nav-menu {
                    width: 70vw;
                    right: -70vw;
                }

                .nav-menu.active {
                    right: 0;
                }
            }

            .nav-menu-content {
                padding: 20px;
                height: 100%;
                display: flex;
                flex-direction: column;
            }

            .nav-links {
                list-style: none;
                padding: 0;
                margin: 40px 0 0 0;
            }

            .nav-links li {
                margin-bottom: 20px;
            }

            .nav-link {
                display: block;
                color: #fbd19a;
                text-decoration: none;
                font-family: 'Montserrat', sans-serif;
                font-weight: 500;
                font-size: 14px;
                padding: 15px 20px;
                border-radius: 8px;
                transition: all 0.3s ease;
                border-left: 4px solid transparent;
            }

            .nav-link:hover {
                background-color: #fbd19a;
                color: #29303b;
                border-left-color: #fbd19a;
                transform: translateX(5px);
            }

            /* Overlay for menu - transparent, only for closing functionality */
            .nav-overlay {
                position: fixed;
                top: 0;
                left: 0;
                width: 100vw;
                height: 100vh;
                background-color: transparent;
                z-index: 999;
                opacity: 0;
                visibility: hidden;
                transition: all 0.3s ease;
            }

            .nav-overlay.active {
                opacity: 1;
                visibility: visible;
            }

            /* Footer Styles - Neue Farbpalette */
            .footer {
                background-color: #04070f;
                padding: 20px 0;
                text-align: center;
                margin-top: 0;
            }

            .footer p {
                font-family: 'Montserrat', sans-serif;
                color: #fbd19a;
                font-size: 14px;
                margin: 0;
            }

            .footer a {
                color: #fbd19a;
                text-decoration: none;
                transition: color 0.3s ease;
            }

            .footer a:hover {
                text-decoration: underline;
                color: #fbd19a;
            }

            /* Mobile Responsiveness - Bleibt gleich */
            @media (max-width: 768px) {
                .header {
                    padding: 0 !important;
                    position: fixed !important;
                    background-color: transparent !important;
                    backdrop-filter: none !important;
                    box-shadow: none !important;
                    width: 100% !important;
                    height: auto !important;
                }

                .header h1 {
                    display: none !important; /* Logo verstecken auf mobil */
                }

                .hamburger-menu {
                    right: 20px !important; /* Immer rechts */
                    left: auto !important;
                    top: 20px !important;
                    transform: none !important;
                    position: fixed !important;
                    z-index: 1003 !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                }

                .hamburger-line {
                    background-color: #fbd19a !important; /* Helle Farbe für bessere Sichtbarkeit */
                }

                .hamburger-menu:hover .hamburger-line {
                    background-color: #fbd19a !important;
                }
            }

            /* Für sehr kleine Bildschirme zusätzliche Anpassungen */
            @media (max-width: 480px) {
                .header {
                    padding: 0 !important;
                    background-color: transparent !important;
                }

                .header h1 {
                    display: none !important; /* Logo auch auf kleinen Screens verstecken */
                }

                .hamburger-menu {
                    right: 15px !important; /* Nach rechts auf kleinen Screens */
                    left: auto !important;
                    top: 15px !important;
                    padding: 6px;
                    position: fixed !important;
                    z-index: 1003 !important;
                    opacity: 1 !important;
                    visibility: visible !important;
                }

                .hamburger-line {
                    width: 22px;
                    background-color: #fbd19a !important;
                }


            }
        `;
        document.head.appendChild(style);
    }

    injectHTML() {
        const headerHTML = `
            <!-- Header -->
            <header class="header" id="header">
                <button class="hamburger-menu" id="hamburgerMenu" aria-label="Menü öffnen">
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                    <span class="hamburger-line"></span>
                </button>
                <h1>SCHWARMVERBUNDEN</h1>
            </header>

            <!-- Navigation Menu -->
            <nav class="nav-menu" id="navMenu">
                <div class="nav-menu-content">
                    <ul class="nav-links">
                        <li><a href="#event" class="nav-link" data-page="index">CO-KREATIVES RETREAT</a></li>
                        <li><a href="#jana" class="nav-link" data-page="index">ÜBER MICH</a></li>
                        <li><a href="impressum.html" class="nav-link">IMPRESSUM & DATENSCHUTZ</a></li>
                    </ul>
                </div>
            </nav>
        `;

        // Insert header at the beginning of body
        document.body.insertAdjacentHTML('afterbegin', headerHTML);
        
        // Get references to elements
        this.header = document.getElementById('header');
        this.navMenu = document.getElementById('navMenu');
        this.hamburgerMenu = document.getElementById('hamburgerMenu');
    }

    injectFooter() {
        const footerHTML = `
            <!-- Footer -->
            <footer class="footer">
                <p>© 2025 <a href="index.html" id="schwarmverbunden-link">Schwarmverbunden</a> I <a href="#jana" data-page="index">Über mich</a> I <a href="impressum.html">Impressum & Datenschutz</a></p>
            </footer>
        `;

        // Insert footer at the end of body
        document.body.insertAdjacentHTML('beforeend', footerHTML);

        // Setup footer navigation
        this.setupFooterNavigation();
    }

    setupFooterNavigation() {
        // Setup Schwarmverbunden link to go to top of index page
        const schwarmverbundenLink = document.getElementById('schwarmverbunden-link');
        if (schwarmverbundenLink) {
            schwarmverbundenLink.addEventListener('click', (e) => {
                e.preventDefault();

                const currentPage = window.location.pathname.split('/').pop() || 'index.html';

                if (currentPage === 'index.html' || currentPage === '') {
                    // We're already on the index page, just scroll to top
                    window.scrollTo({ top: 0, behavior: 'smooth' });
                } else {
                    // We're on a different page, navigate to index.html
                    window.location.href = 'index.html';
                }
            });
        }

        // Keep existing functionality for other footer links with data-page attribute
        const footerLinks = document.querySelectorAll('.footer a[data-page]');
        footerLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                const dataPage = link.getAttribute('data-page');

                // Handle cross-page navigation for anchor links
                if (dataPage === 'index' && href.startsWith('#')) {
                    const currentPage = window.location.pathname.split('/').pop() || 'index.html';

                    if (currentPage !== 'index.html' && currentPage !== '') {
                        // We're on a different page, need to navigate to index.html first
                        e.preventDefault();
                        window.location.href = 'index.html' + href;
                        return;
                    }
                }
            });
        });
    }

    setupEventListeners() {
        // Header title click
        const headerTitle = this.header.querySelector('h1');
        if (headerTitle) {
            headerTitle.addEventListener('click', () => {
                window.location.href = 'index.html';
            });
        }
    }



    setupHamburgerMenu() {
        if (!this.hamburgerMenu || !this.navMenu) return;

        // Create overlay element
        this.overlay = document.createElement('div');
        this.overlay.className = 'nav-overlay';
        this.overlay.id = 'navOverlay';
        document.body.appendChild(this.overlay);

        // Toggle menu on hamburger click
        this.hamburgerMenu.addEventListener('click', () => {
            if (this.navMenu.classList.contains('active')) {
                this.closeMenu();
            } else {
                this.openMenu();
            }
        });

        // Close menu when clicking overlay (now transparent)
        this.overlay.addEventListener('click', () => this.closeMenu());

        // Close menu when clicking anywhere outside the menu
        document.addEventListener('click', (event) => {
            if (this.navMenu.classList.contains('active')) {
                // Don't close if clicking on the menu itself or hamburger button
                if (!this.navMenu.contains(event.target) && !this.hamburgerMenu.contains(event.target)) {
                    this.closeMenu();
                }
            }
        });

        // Close menu when clicking nav links
        const navLinks = this.navMenu.querySelectorAll('.nav-link');
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                const href = link.getAttribute('href');
                const dataPage = link.getAttribute('data-page');

                // Handle cross-page navigation for anchor links
                if (dataPage === 'index' && href.startsWith('#')) {
                    const currentPage = window.location.pathname.split('/').pop() || 'index.html';

                    if (currentPage !== 'index.html' && currentPage !== '') {
                        // We're on a different page, need to navigate to index.html first
                        e.preventDefault();
                        window.location.href = 'index.html' + href;
                        return;
                    }
                }

                this.closeMenu();
            });
        });

        // Close menu on escape key
        document.addEventListener('keydown', (event) => {
            if (event.key === 'Escape' && this.navMenu.classList.contains('active')) {
                this.closeMenu();
            }
        });
    }

    // Open menu method
    openMenu() {
        // Always position menu at top of viewport
        this.navMenu.style.top = '0px';

        this.navMenu.classList.add('active');
        this.overlay.classList.add('active');

        // Prevent body scrolling and layout shift
        const scrollbarWidth = window.innerWidth - document.documentElement.clientWidth;
        document.body.style.paddingRight = scrollbarWidth + 'px';
        document.body.style.overflow = 'hidden';
    }

    // Close menu method
    closeMenu() {
        this.navMenu.classList.remove('active');
        this.overlay.classList.remove('active');
        document.body.style.overflow = '';
        document.body.style.paddingRight = '';
    }

    setActiveNavigation() {
        const currentPage = window.location.pathname.split('/').pop() || 'index.html';
        const navLinks = this.navMenu.querySelectorAll('.nav-link');

        navLinks.forEach(link => {
            link.classList.remove('active');
            const linkHref = link.getAttribute('href');

            if (linkHref === currentPage ||
                (currentPage === '' && linkHref === 'index.html') ||
                (currentPage === 'index.html' && linkHref === 'index.html')) {
                link.classList.add('active');
            }
        });
    }


}

// Initialize header and footer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new HeaderAndFooterManager();
});
